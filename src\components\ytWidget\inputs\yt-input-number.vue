<template>
  <wd-cell
    :disabled="actualDisabled"
    :required="isRequired"
    :title="actualLabel"
    custom-class="input-number-cell"
  >
    <wd-input-number
      v-model="inputValue"
      :adjust-position="item.props?.adjustPosition !== false"
      :allow-null="true"
      :before-change="handleBeforeChange"
      :disable-input="item.props?.disableInput || false"
      :disable-minus="item.props?.disableMinus || false"
      :disable-plus="item.props?.disablePlus || false"
      :disabled="actualDisabled"
      :error-message="errorMessage"
      :immediate-change="item.props?.immediateChange !== false"
      :input-type="item.props?.inputType || 'digit'"
      :input-width="item.props?.inputWidth || '50px'"
      :long-press="item.props?.longPress || false"
      :placeholder="actualPlaceholder"
      :step-strictly="item.props?.stepStrictly || false"
      :update-on-init="item.props?.updateOnInit !== false"
      :without-input="item.props?.withoutInput || false"
      class="input-number-widget"
      v-bind="{
        ...(actualMin !== undefined ? { min: actualMin } : {}),
        ...(actualMax !== undefined ? { max: actualMax } : {}),
        ...(actualStep !== undefined ? { step: actualStep } : {}),
        ...(actualPrecision !== undefined ? { precision: actualPrecision } : {}),
      }"
      @blur="handleBlur"
      @focus="handleFocus"
    />
  </wd-cell>

  <!-- 信息提示 -->
  <view v-if="item.info" class="notice-bar">
    <wd-notice-bar :scrollable="false" :text="item.info" prefix="warn-bold" type="info" wrapable />
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { FormItem } from '@/types/form'

interface Props {
  item: FormItem
  modelValue?: number | string
  // 保留一些可能的直接覆盖属性以提供灵活性
  label?: string
  placeholder?: string
  required?: boolean
  min?: number
  max?: number
  step?: number
  precision?: number
  disabled?: boolean
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  label: '',
  placeholder: '',
  required: false,
  disabled: false,
  readonly: false,
  // 设置 precision 的默认值为 2
  precision: 2, // 修改: 设置默认精度为 2
})

const emit = defineEmits<{
  'update:modelValue': [value: number | string]
  change: [value: number | string]
  focus: [event: Event]
  blur: [event: Event]
}>()

// 🚀 使用 computed setter/getter 模式 - 更优雅，无循环问题
const inputValue = computed({
  get: () => {
    // 当允许空值且当前值为undefined/null/空字符串时，返回空字符串
    if (
      actualAllowNull.value &&
      (props.modelValue === undefined || props.modelValue === null || props.modelValue === '')
    ) {
      return ''
    }
    // 如果不允许空值但值为空，返回null让wot-design-uni组件处理默认值
    if (
      !actualAllowNull.value &&
      (props.modelValue === undefined || props.modelValue === null || props.modelValue === '')
    ) {
      return null
    }
    return props.modelValue
  },
  set: (value: number | string) => {
    emit('update:modelValue', value)
    emit('change', value)
  },
})

// 计算实际使用的属性值（优先级：props > item.props > 默认值）
const actualLabel = computed(() => {
  return props.label || props.item?.title || '计数器'
})

const actualPlaceholder = computed(() => {
  // 当允许空值时，默认占位符提示"请输入数值"或自定义文本
  const defaultPlaceholder = '请输入数值'
  return (
    props.placeholder || props.item?.props?.placeholder || props.item?.info || defaultPlaceholder
  )
})

// 计算是否允许空值
const actualAllowNull = computed(() => {
  return props.item?.props?.allowNull || false
})

// 计算是否必填
const isRequired = computed(() => {
  if (props.required) return true
  if (typeof props.item.$required === 'boolean') {
    return props.item.$required
  }
  return !!props.item.$required // 字符串值转布尔
})

// 计算错误提示信息
const errorMessage = computed(() => {
  if (typeof props.item.$required === 'string') {
    return props.item.$required
  }
  return ''
})

const actualMin = computed(() => {
  // 只有明确设置了min值时才使用，避免强制设置默认值
  if (props.min !== undefined) return props.min
  if (props.item?.props?.min !== undefined) return props.item.props.min
  // 不设置默认值，让wot-design-uni使用其自身的默认值
  return undefined
})

const actualMax = computed(() => {
  // 只有明确设置了max值时才使用
  if (props.max !== undefined) return props.max
  if (props.item?.props?.max !== undefined) return props.item.props.max
  return undefined // 让wot-design-uni使用默认的Infinity
})

const actualStep = computed(() => {
  // 只有明确设置了step值时才使用
  if (props.step !== undefined) return props.step
  if (props.item?.props?.step !== undefined) return props.item.props.step
  return undefined // 让wot-design-uni使用默认的1
})

const actualPrecision = computed(() => {
  // 只有明确设置了precision值时才使用
  if (props.precision !== undefined) return props.precision
  if (props.item?.props?.precision !== undefined) return props.item.props.precision
  // 返回默认精度 2
  return 2 // 修改: 返回默认精度 2
})

const actualDisabled = computed(() => {
  return props.disabled || props.readonly || props.item?.props?.disabled || false
})

// 处理焦点事件
const handleFocus = (e: Event) => {
  emit('focus', e)
}

// 处理失焦事件
const handleBlur = (e: Event) => {
  emit('blur', e)
}

// 处理异步变更
const handleBeforeChange = (value: number | string) => {
  // 如果配置了自定义的beforeChange函数
  if (props.item?.props?.beforeChange && typeof props.item.props.beforeChange === 'function') {
    return props.item.props.beforeChange(value)
  }
  return true
}
</script>

<style lang="scss" scoped>
.input-number-cell {
  ::v-deep(.wd-cell__value) {
    display: flex;
    justify-content: flex-end;
  }
}

.input-number-widget {
  max-width: 100%;
}

.notice-bar {
  padding: 20rpx;
}
</style>
